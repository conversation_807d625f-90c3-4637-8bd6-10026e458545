{"name": "teacher-cms-server", "version": "1.0.0", "description": "Local server for Teacher CMS with MongoDB", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "mongodb": "^6.3.0", "multer": "^1.4.5-lts.1", "sharp": "^0.33.2", "socket.io": "^4.7.4", "compression": "^1.7.4", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.2"}}