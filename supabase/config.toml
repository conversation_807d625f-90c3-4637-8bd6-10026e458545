
project_id = "tjgyskkfhmcabtscsbvh"

[auth]
# Fix auth_otp_long_expiry warning - set OTP expiry to 30 minutes (1800 seconds)
email_otp_expiry = 1800

# Fix auth_leaked_password_protection warning - enable password protection
enable_leaked_password_protection = true

# Additional security settings
enable_signup = true
enable_confirmations = true
enable_recoveries = true

[auth.email]
# Enable email provider
enable_signup = true
double_confirm_changes = true
enable_confirmations = true

# Email template configuration
[auth.email.template]
[auth.email.template.invite]
subject = "You have been invited"
content_path = "./supabase/templates/invite.html"

[auth.email.template.confirmation]
subject = "Confirm your signup"
content_path = "./supabase/templates/confirmation.html"

[auth.email.template.recovery]
subject = "Reset your password"
content_path = "./supabase/templates/recovery.html"

[auth.email.template.email_change]
subject = "Confirm email change"
content_path = "./supabase/templates/email_change.html"
