
# Supabase Configuration
# Copy this file to .env.local and fill in your actual values

# Supabase Project URL
VITE_SUPABASE_URL=https://your-project-ref.supabase.co

# Supabase Anon/Public Key
VITE_SUPABASE_ANON_KEY=your-anon-key-here

# For production deployment on Vercel:
# 1. Go to your Vercel project settings
# 2. Navigate to Environment Variables
# 3. Add these variables with your actual Supabase values:
#    - VITE_SUPABASE_URL
#    - VITE_SUPABASE_ANON_KEY
# 4. Redeploy your application

# Important: Never commit real API keys to version control
# This file shows the required environment variable names only

# Additional deployment notes:
# - Ensure your Supabase project's Site URL is set to your Vercel domain
# - Add your Vercel domain to Supabase's Redirect URLs
# - Both staging and production URLs should be configured in Supabase
