-- Enable UUID extension
create extension if not exists "uuid-ossp";

-- Create profiles table first (if not exists)
create table if not exists profiles (
  id uuid references auth.users(id) on delete cascade primary key,
  email text,
  full_name text,
  role text check (role in ('teacher', 'student', 'admin')) default 'student',
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Enable RLS on profiles
alter table profiles enable row level security;

-- Create profiles RLS policies
create policy if not exists "Users can view own profile" on profiles
  for select using (auth.uid() = id);

create policy if not exists "Users can update own profile" on profiles
  for update using (auth.uid() = id);

create policy if not exists "Users can insert own profile" on profiles
  for insert with check (auth.uid() = id);

-- Create subjects table
create table if not exists subjects (
  id uuid default uuid_generate_v4() primary key,
  name text not null,
  description text,
  grade integer not null,
  icon text,
  color text,
  created_by uuid references profiles(id),
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create chapters table
create table if not exists chapters (
  id uuid default uuid_generate_v4() primary key,
  name text not null,
  description text,
  subject_id uuid references subjects(id),
  order_index integer not null,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create study_materials table
create table if not exists study_materials (
  id uuid default uuid_generate_v4() primary key,
  teacher_id uuid references profiles(id) not null,
  title text not null,
  description text,
  type text check (type in ('textbook', 'video', 'summary', 'ppt', 'quiz')) not null,
  url text,
  file_path text,
  subject_id uuid references subjects(id),
  chapter_id uuid references chapters(id),
  grade integer,
  is_public boolean default true,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create RLS policies for subjects
alter table subjects enable row level security;
create policy if not exists "Public subjects are viewable"
  on subjects for select
  using ( true );
create policy if not exists "Teachers can manage their own subjects"
  on subjects for all
  using ( auth.uid() = created_by )
  with check ( auth.uid() = created_by );

-- Create RLS policies for chapters
alter table chapters enable row level security;
create policy if not exists "Public chapters are viewable"
  on chapters for select
  using ( true );
create policy if not exists "Teachers can manage chapters for their subjects"
  on chapters for all
  using (
    exists (
      select 1 from subjects
      where subjects.id = chapters.subject_id
      and subjects.created_by = auth.uid()
    )
  )
  with check (
    exists (
      select 1 from subjects
      where subjects.id = chapters.subject_id
      and subjects.created_by = auth.uid()
    )
  );

-- Create RLS policies for study_materials
alter table study_materials enable row level security;
create policy if not exists "Public materials are viewable"
  on study_materials for select
  using ( is_public = true );
create policy if not exists "Teachers can manage their own materials"
  on study_materials for all
  using ( auth.uid() = teacher_id )
  with check ( auth.uid() = teacher_id );

-- Create helper functions
create or replace function public.get_current_user_role()
returns text
language sql
stable security definer
set search_path = public
as $$
  select role from public.profiles where id = auth.uid();
$$;

create or replace function public.is_teacher()
returns boolean
language sql
stable security definer
set search_path = public
as $$
  select exists (
    select 1 from public.profiles
    where id = auth.uid() and role = 'teacher'
  );
$$;

create or replace function public.is_admin()
returns boolean
language sql
stable security definer
set search_path = public
as $$
  select exists (
    select 1 from public.profiles
    where id = auth.uid() and role = 'admin'
  );
$$;
