-- Enable UUID extension
create extension if not exists "uuid-ossp";

-- Create subjects table
create table subjects (
  id uuid default uuid_generate_v4() primary key,
  name text not null,
  description text,
  grade integer not null,
  icon text,
  color text,
  created_by text,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create chapters table
create table chapters (
  id uuid default uuid_generate_v4() primary key,
  name text not null,
  description text,
  subject_id uuid references subjects(id),
  order_index integer not null,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create study_materials table
create table study_materials (
  id uuid default uuid_generate_v4() primary key,
  teacher_id text not null,
  title text not null,
  description text,
  type text check (type in ('textbook', 'video', 'summary', 'ppt', 'quiz')) not null,
  url text,
  file_path text,
  subject_id uuid references subjects(id),
  chapter_id uuid references chapters(id),
  grade integer,
  is_public boolean default true,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create RLS policies for subjects
alter table subjects enable row level security;
create policy "Public subjects are viewable"
  on subjects for select
  using ( true );
create policy "Users can insert subjects"
  on subjects for insert
  with check ( true );
create policy "Users can update subjects"
  on subjects for update
  using ( true );

-- Create RLS policies for chapters
alter table chapters enable row level security;
create policy "Public chapters are viewable"
  on chapters for select
  using ( true );
create policy "Users can insert chapters"
  on chapters for insert
  with check ( true );
create policy "Users can update chapters"
  on chapters for update
  using ( true );

-- Create RLS policies for study_materials
alter table study_materials enable row level security;
create policy "Public materials are viewable"
  on study_materials for select
  using ( true );
create policy "Users can insert materials"
  on study_materials for insert
  with check ( auth.uid() = teacher_id );
create policy "Users can update their own materials"
  on study_materials for update
  using ( auth.uid() = teacher_id );
