-- Complete database setup script for K-12 Learning Platform
-- Run this script in your Supabase SQL editor

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create profiles table (extends auth.users)
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT,
  full_name TEXT,
  role TEXT CHECK (role IN ('teacher', 'student', 'admin')) DEFAULT 'student',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Enable RLS on profiles
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Create profiles RLS policies
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
CREATE POLICY "Users can view own profile" ON public.profiles
  FOR SELECT USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
CREATE POLICY "Users can update own profile" ON public.profiles
  FOR UPDATE USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;
CREATE POLICY "Users can insert own profile" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Create subjects table
CREATE TABLE IF NOT EXISTS public.subjects (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  grade INTEGER NOT NULL,
  icon TEXT,
  color TEXT,
  created_by UUID REFERENCES public.profiles(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Enable RLS on subjects
ALTER TABLE public.subjects ENABLE ROW LEVEL SECURITY;

-- Create subjects RLS policies
DROP POLICY IF EXISTS "Public subjects are viewable" ON public.subjects;
CREATE POLICY "Public subjects are viewable" ON public.subjects
  FOR SELECT USING (true);

DROP POLICY IF EXISTS "Teachers can manage their own subjects" ON public.subjects;
CREATE POLICY "Teachers can manage their own subjects" ON public.subjects
  FOR ALL USING (auth.uid() = created_by)
  WITH CHECK (auth.uid() = created_by);

-- Create chapters table
CREATE TABLE IF NOT EXISTS public.chapters (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  subject_id UUID REFERENCES public.subjects(id),
  order_index INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Enable RLS on chapters
ALTER TABLE public.chapters ENABLE ROW LEVEL SECURITY;

-- Create chapters RLS policies
DROP POLICY IF EXISTS "Public chapters are viewable" ON public.chapters;
CREATE POLICY "Public chapters are viewable" ON public.chapters
  FOR SELECT USING (true);

DROP POLICY IF EXISTS "Teachers can manage chapters for their subjects" ON public.chapters;
CREATE POLICY "Teachers can manage chapters for their subjects" ON public.chapters
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.subjects 
      WHERE subjects.id = chapters.subject_id 
      AND subjects.created_by = auth.uid()
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.subjects 
      WHERE subjects.id = chapters.subject_id 
      AND subjects.created_by = auth.uid()
    )
  );

-- Create study_materials table
CREATE TABLE IF NOT EXISTS public.study_materials (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  teacher_id UUID REFERENCES public.profiles(id) NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  type TEXT CHECK (type IN ('textbook', 'video', 'summary', 'ppt', 'quiz')) NOT NULL,
  url TEXT,
  file_path TEXT,
  subject_id UUID REFERENCES public.subjects(id),
  chapter_id UUID REFERENCES public.chapters(id),
  grade INTEGER,
  is_public BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Enable RLS on study_materials
ALTER TABLE public.study_materials ENABLE ROW LEVEL SECURITY;

-- Create study_materials RLS policies
DROP POLICY IF EXISTS "Public materials are viewable" ON public.study_materials;
CREATE POLICY "Public materials are viewable" ON public.study_materials
  FOR SELECT USING (is_public = true);

DROP POLICY IF EXISTS "Teachers can manage their own materials" ON public.study_materials;
CREATE POLICY "Teachers can manage their own materials" ON public.study_materials
  FOR ALL USING (auth.uid() = teacher_id)
  WITH CHECK (auth.uid() = teacher_id);

-- Create helper functions
CREATE OR REPLACE FUNCTION public.get_current_user_role()
RETURNS TEXT
LANGUAGE SQL
STABLE SECURITY DEFINER
SET search_path = public
AS $$
  SELECT role FROM public.profiles WHERE id = auth.uid();
$$;

CREATE OR REPLACE FUNCTION public.is_teacher()
RETURNS BOOLEAN
LANGUAGE SQL
STABLE SECURITY DEFINER
SET search_path = public
AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = 'teacher'
  );
$$;

CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN
LANGUAGE SQL
STABLE SECURITY DEFINER
SET search_path = public
AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin'
  );
$$;

-- Create function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name, role)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'role', 'student')
  );
  RETURN NEW;
END;
$$;

-- Create trigger for new user registration
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Insert sample data
INSERT INTO public.subjects (name, description, grade, icon, color) VALUES
  ('Mathematics', 'Class 8 Mathematics curriculum', 8, '🧮', '#2979FF'),
  ('Science', 'Class 8 Science curriculum', 8, '🔬', '#00E676'),
  ('English', 'Class 8 English curriculum', 8, '📖', '#FFA726'),
  ('Mathematics', 'Class 9 Mathematics curriculum', 9, '🧮', '#2979FF'),
  ('Science', 'Class 9 Science curriculum', 9, '🔬', '#00E676'),
  ('English', 'Class 9 English curriculum', 9, '📖', '#FFA726'),
  ('Mathematics', 'Class 10 Mathematics curriculum', 10, '🧮', '#2979FF'),
  ('Science', 'Class 10 Science curriculum', 10, '🔬', '#00E676'),
  ('English', 'Class 10 English curriculum', 10, '📖', '#FFA726')
ON CONFLICT DO NOTHING;

-- Insert sample chapters for Mathematics Class 8
DO $$
DECLARE
  math_subject_id UUID;
BEGIN
  SELECT id INTO math_subject_id FROM public.subjects WHERE name = 'Mathematics' AND grade = 8 LIMIT 1;
  
  IF math_subject_id IS NOT NULL THEN
    INSERT INTO public.chapters (name, description, subject_id, order_index) VALUES
      ('Chapter 1: Rational Numbers', 'Understanding rational numbers and their operations', math_subject_id, 1),
      ('Chapter 2: Linear Equations in One Variable', 'Solving linear equations', math_subject_id, 2),
      ('Chapter 3: Understanding Quadrilaterals', 'Properties of quadrilaterals', math_subject_id, 3),
      ('Chapter 4: Practical Geometry', 'Constructing geometric figures', math_subject_id, 4)
    ON CONFLICT DO NOTHING;
  END IF;
END $$;

-- Create storage bucket for study materials (if not exists)
INSERT INTO storage.buckets (id, name, public) VALUES ('study-materials', 'study-materials', true)
ON CONFLICT DO NOTHING;

-- Create storage policy for study materials
DROP POLICY IF EXISTS "Public Access" ON storage.objects;
CREATE POLICY "Public Access" ON storage.objects
  FOR SELECT USING (bucket_id = 'study-materials');

DROP POLICY IF EXISTS "Teachers can upload" ON storage.objects;
CREATE POLICY "Teachers can upload" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'study-materials' AND
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'teacher'
    )
  );

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;

-- Success message
SELECT 'Database setup completed successfully!' as message;
