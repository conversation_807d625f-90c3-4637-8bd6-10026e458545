import { supabaseClient as supabase } from '@/integrations/supabase/client';
import type { Database } from '@/integrations/supabase/database.types';
import type {
  StudyMaterialBase,
  StudyMaterialType as MaterialType,
  StudyMaterialWithRelations,
  LocalStudyMaterial,
  StudyMaterialResponse
} from '@/types/studyMaterial';

// Re-export types for external use
export type { Subject, Chapter };

/**
 * Custom error class for DataService related errors
 */

class DataServiceError extends Error {
  /**
   * Creates a new DataServiceError
   * @param message - Error message
   * @param code - Optional error code
   */
  constructor(message: string, public code?: string) {
    super(message);
    this.name = 'DataServiceError';
  }
}

/**
 * Represents a subject in the curriculum
 */
interface Subject {
  id: string;
  name: string;
  description?: string;
  grade: number;
  created_at: string;
  updated_at: string;
}

/**
 * Represents a chapter within a subject
 */
interface Chapter {
  id: string;
  subject_id: string;
  name: string;
  order?: number;
  description?: string;
  created_at: string;
  updated_at: string;
  subjects?: Subject;
}

/**
 * Standard response format for chapter-related operations
 */
interface ChapterResponse {
  data: Chapter[] | null;
  error: Error | null;
  count: number;
}

/**
 * Filter options for querying study materials
 */
type QueryFilters = {
  grade?: number;
  subject_id?: string;
  chapter_id?: string;
  type?: MaterialType;
  teacher_id?: string;
  is_public?: boolean;
};

/**
 * Service class for handling data operations with the Supabase database
 */
export class DataService {
  /**
   * Maximum file size in MB for uploads
   */
  private static readonly MAX_FILE_SIZE_MB = 10;
  
  /**
   * Allowed file extensions for uploads
   */
  private static readonly ALLOWED_FILE_TYPES = [
    'pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx',
    'jpg', 'jpeg', 'png', 'gif', 'mp4', 'mov', 'avi'
  ];
  /**
   * Fetches study materials with optional filters
   * @param filters - Optional filters to apply to the query
   * @returns Promise with study materials data, error, and count
   */
  static async getStudyMaterials(
    filters?: QueryFilters
  ): Promise<{ data: StudyMaterialWithRelations[] | null; error: Error | null; count: number }> {
    try {
      if (!supabase) {
        console.warn('Supabase client not configured. Returning empty study materials.');
        return { data: [], error: null, count: 0 };
      }

      let query = supabase
        .from('study_materials')
        .select(
          `
          *,
          subjects (*),
          profiles (full_name)
        `,
          { count: 'exact' }
        )
        .eq('is_public', true);

      if (filters) {
        const { grade, subject_id, chapter_id, type, teacher_id } = filters;
        if (grade !== undefined) query = query.eq('grade', grade);
        if (subject_id) query = query.eq('subject_id', subject_id);
        if (chapter_id) query = query.eq('chapter_id', chapter_id);
        if (type) query = query.eq('type', type);
        if (teacher_id) query = query.eq('teacher_id', teacher_id);
      }

      const { data, error, count } = await query;

      if (error) {
        console.error('Error fetching study materials:', error);
        return { data: null, error, count: 0 };
      }

      return { 
        data: data as StudyMaterialWithRelations[], 
        error: null, 
        count: count || 0 
      };
    } catch (error) {
      console.error('Failed to fetch study materials:', error);
      return {
        data: null,
        error: error instanceof Error ? error : new Error('Unknown error'),
        count: 0
      };
    }
  }

  static async getStudyMaterialById(
    id: string
  ): Promise<{ data: StudyMaterialWithRelations[] | null; error: Error | null; count: number }> {
    try {
      if (!supabase) {
        throw new Error('Supabase client is not initialized');
      }

      const { data, error } = await supabase
        .from('study_materials')
        .select(
          `
          *,
          subjects (*),
          profiles (full_name)
        `
        )
        .eq('id', id)
        .single();

      if (error) {
        throw new DataServiceError(
          `Failed to fetch study material: ${error.message}`,
          error.code || 'NOT_FOUND'
        );
      }

      if (!data) {
        throw new DataServiceError('Study material not found', 'NOT_FOUND');
      }

      // Type assertion to ensure data matches our expected type
      const typedData = data as StudyMaterialWithRelations;

      return {
        data: [typedData],
        error: null,
        count: 1,
      };
    } catch (error) {
      console.error(`Error fetching study material with id ${id}:`, error);
      return {
        data: null,
        error: error instanceof Error ? error : new Error('Unknown error occurred'),
        count: 0,
      };
    }
  }

  static async createStudyMaterial(
    material: Omit<StudyMaterialBase, 'id' | 'created_at' | 'updated_at'>
  ): Promise<{ data: StudyMaterialWithRelations[] | null; error: Error | null; count: number }> {
    try {
      if (!supabase) {
        throw new Error('Supabase client is not initialized');
      }

      const { data, error } = await supabase
        .from('study_materials')
        .insert([material])
        .select()
        .single();

      if (error) {
        throw new DataServiceError(
          `Failed to create study material: ${error.message}`,
          error.code || 'CREATE_FAILED'
        );
      }

      // Fetch the created material with relations
      return this.getStudyMaterialById(data.id);
    } catch (error) {
      console.error('Error creating study material:', error);
      return {
        data: null,
        error: error instanceof Error ? error : new Error('Unknown error occurred'),
        count: 0,
      };
    }
  }

  static async updateStudyMaterial(
    id: string,
    updates: Partial<Omit<StudyMaterialBase, 'id' | 'created_at' | 'updated_at'>>
  ): Promise<{ data: StudyMaterialWithRelations[] | null; error: Error | null; count: number }> {
    try {
      if (!supabase) {
        throw new Error('Supabase client is not initialized');
      }

      const { error } = await supabase
        .from('study_materials')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id);

      if (error) {
        throw new DataServiceError(
          `Failed to update study material: ${error.message}`,
          error.code || 'UPDATE_FAILED'
        );
      }

      // Fetch the updated material with relations
      return this.getStudyMaterialById(id);
    } catch (error) {
      console.error(`Error updating study material with id ${id}:`, error);
      return {
        data: null,
        error: error instanceof Error ? error : new Error('Unknown error occurred'),
        count: 0,
      };
    }
  }

  static async deleteStudyMaterial(id: string): Promise<{ error: Error | null; count: number }> {
    try {
      if (!supabase) {
        throw new Error('Supabase client is not initialized');
      }

      const { error, count } = await supabase
        .from('study_materials')
        .delete({ count: 'exact' })
        .eq('id', id);

      if (error) {
        throw new DataServiceError(
          `Failed to delete study material: ${error.message}`,
          error.code || 'DELETE_FAILED'
        );
      }

      return { error: null, count: count || 1 };
    } catch (error) {
      console.error(`Error deleting study material with id ${id}:`, error);
      return {
        error: error instanceof Error ? error : new Error('Unknown error occurred'),
        count: 0
      };
    }
  }

  static async getSubjects(grade?: number): Promise<{ data: Subject[] | null; error: Error | null; count: number }> {
    try {
      if (!supabase) {
        console.warn('Supabase client not configured. Returning empty subjects.');
        return { data: [], error: null, count: 0 };
      }
      
      let query = supabase
        .from('subjects')
        .select('*')
        .order('grade', { ascending: true });
        
      if (grade) {
        query = query.eq('grade', grade);
      }
      
      const { data, error, count } = await query;
      
      if (error) {
        console.warn('Error fetching subjects:', error);
        return { data: [], error, count: 0 };
      }
      
      return { data, error: null, count: count || 0 };
    } catch (error) {
      console.warn('Error in getSubjects:', error);
      return {
        data: [],
        error: error instanceof Error ? error : new Error('Unknown error'),
        count: 0
      };
    }
  }

  static async getChapters(subjectId: string): Promise<ChapterResponse> {
    try {
      if (!supabase) {
        console.warn('Supabase client not configured. Returning empty chapters.');
        return { data: [], error: null, count: 0 };
      }
      
      const { data, error, count } = await supabase
        .from('chapters')
        .select('*, subjects!inner(*)', { count: 'exact' })
        .eq('subject_id', subjectId)
        .order('order_index', { ascending: true });
      
      if (error) {
        console.error('Error fetching chapters:', error);
        return { data: null, error, count: 0 };
      }

      return { 
        data: data as Chapter[], 
        error: null,
        count: count || 0
      };
    } catch (error) {
      console.error('Failed to fetch chapters:', error);
      return {
        data: null,
        error: error instanceof Error ? error : new Error('Unknown error'),
        count: 0
      };
    }
  }

  static async createChapter(
    chapter: Omit<Chapter, 'id' | 'created_at' | 'updated_at'> & { order_index?: number }
  ): Promise<{ data: Chapter[] | null; error: Error | null; count: number }> {
    try {
      if (!supabase) {
        throw new Error('Supabase client not initialized');
      }

      const { data, error } = await supabase
        .from('chapters')
        .insert([{
          ...chapter,
          order_index: chapter.order_index || 0,
          description: chapter.description || ''
        }])
        .select()
        .single();

      if (error) {
        console.error('Error creating chapter:', error);
        return { data: null, error, count: 0 };
      }

      return { 
        data: [data as Chapter], 
        error: null,
        count: 1
      };
    } catch (error) {
      console.error('Failed to create chapter:', error);
      return {
        data: null,
        error: error instanceof Error ? error : new Error('Unknown error'),
        count: 0
      };
    }
  }

  static async updateChapter(
    id: string, 
    updates: Partial<Omit<Chapter, 'id' | 'created_at' | 'updated_at'>>
  ): Promise<{ data: Chapter[] | null; error: Error | null; count: number }> {
    try {
      if (!supabase) {
        throw new Error('Supabase client not initialized');
      }

      const { data, error } = await supabase
        .from('chapters')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating chapter:', error);
        return { data: null, error, count: 0 };
      }

      return { 
        data: data ? [data as Chapter] : null, 
        error: null,
        count: data ? 1 : 0
      };
    } catch (error) {
      console.error('Failed to update chapter:', error);
      return {
        data: null,
        error: error instanceof Error ? error : new Error('Unknown error'),
        count: 0
      };
    }
  }

  static async deleteChapter(id: string): Promise<{ error: Error | null; count: number }> {
    try {
      if (!supabase) {
        return { error: new Error('Supabase client not initialized'), count: 0 };
      }

      const { error, count } = await supabase
        .from('chapters')
        .delete({ count: 'exact' })
        .eq('id', id);

      if (error) {
        console.error('Error deleting chapter:', error);
        return { error, count: 0 };
      }

      return { error: null, count: count || 1 };
    } catch (error) {
      console.error('Failed to delete chapter:', error);
      return {
        error: error instanceof Error ? error : new Error('Unknown error'),
        count: 0
      };
    }
  }

  /**
   * Uploads a file to Supabase Storage
   * @param bucket - The storage bucket name
   * @param path - The path where to store the file
   * @param file - The file to upload
   * @returns Promise with upload result data and error if any
   * @throws {DataServiceError} If file validation fails
   */
  static async uploadFile(
    bucket: string, 
    path: string, 
    file: File
  ): Promise<{ 
    data: { 
      path: string;
      id: string;
      fullPath: string;
    } | null; 
    error: Error | null 
  }> {
    // Validate file type and size
    const isValidType = DataService.validateFileType(file.name, DataService.ALLOWED_FILE_TYPES);
    if (!isValidType) {
      throw new DataServiceError(
        `Invalid file type. Allowed types: ${DataService.ALLOWED_FILE_TYPES.join(', ')}`,
        'INVALID_FILE_TYPE'
      );
    }

    const isValidSize = DataService.validateFileSize(file, DataService.MAX_FILE_SIZE_MB);
    if (!isValidSize) {
      throw new DataServiceError(
        `File too large. Maximum size: ${DataService.MAX_FILE_SIZE_MB}MB`,
        'FILE_TOO_LARGE'
      );
    }

    if (!supabase) {
      throw new Error('Supabase client not initialized');
    }
    try {
      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(path, file);
      
      if (error) {
        console.error('Error uploading file:', error);
        throw error;
      }
      
      return { 
        data: {
          path: data.path,
          id: data.id,
          fullPath: `${bucket}/${data.path}`
        }, 
        error: null 
      };
    } catch (error) {
      console.error('File upload failed:', error);
      return { 
        data: null, 
        error: error instanceof Error ? error : new Error('Unknown error during file upload') 
      };
    }
  }

  /**
   * Generates a public URL for a file in storage
   * @param bucket - The storage bucket name
   * @param path - The file path in the bucket
   * @returns Public URL for the file or empty string on error
   */
  static getFileUrl(bucket: string, path: string): string {
    if (!supabase) {
      console.error('Supabase client not initialized');
      return '';
    }
    try {
      const { data } = supabase.storage
        .from(bucket)
        .getPublicUrl(path);
      return data?.publicUrl || '';
    } catch (error) {
      console.error('Error generating file URL:', error);
      return '';
    }
  }

  // Google Drive URL utilities
  static isGoogleDriveUrl(url: string): boolean {
    return url.includes('drive.google.com') || url.includes('docs.google.com');
  }

  static convertGoogleDriveUrl(url: string): string {
    if (!url) return '';
    // Convert various Google Drive URL formats to a consistent viewable format
    if (url.includes('drive.google.com')) {
      const fileId = url.match(/\/d\/([a-zA-Z0-9-_]+)/)?.[1];
      if (fileId) {
        return `https://drive.google.com/file/d/${fileId}/view?usp=sharing`;
      }
    }
    return url;
  }

  static getGoogleDriveEmbedUrl(url: string): string {
    if (!url) return '';
    // Convert Google Drive URL to embeddable format
    if (url.includes('drive.google.com')) {
      const fileId = url.match(/\/d\/([a-zA-Z0-9-_]+)/)?.[1];
      if (fileId) {
        return `https://drive.google.com/file/d/${fileId}/preview`;
      }
    }
    return url;
  }

  // File validation utilities
  static validateFileType(fileName: string, allowedTypes: string[]): boolean {
    if (!fileName) return false;
    const extension = fileName.split('.').pop()?.toLowerCase();
    return extension ? allowedTypes.includes(extension) : false;
  }

  static validateFileSize(file: File, maxSizeMB: number): boolean {
    if (!file) return false;
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    return file.size <= maxSizeBytes;
  }

  // Content validation
  static validateContentData(material: Partial<StudyMaterialWithRelations>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!material?.title?.trim()) {
      errors.push('Title is required');
    }

    if (!material?.type) {
      errors.push('Content type is required');
    }

    if (!material?.subject_id) {
      errors.push('Subject is required');
    }

    if (!material?.chapter_id) {
      errors.push('Chapter is required');
    }

    if (!material?.grade || material.grade < 1 || material.grade > 12) {
      errors.push('Valid grade (1-12) is required');
    }

    // Validate URL for certain types
    if (material.type === 'video' || material.type === 'quiz') {
      if (!material.url?.trim()) {
        errors.push(`URL is required for ${material.type} content`);
      } else if (!DataService.isValidUrl(material.url)) {
        errors.push('Please provide a valid URL');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  private static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
}

export const dataService = new DataService();
