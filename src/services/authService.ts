
import { supabaseClient as supabase, isSupabaseConfigured } from '@/integrations/supabase/client';

export interface Profile {
  id: string;
  email?: string;
  full_name?: string;
  role: 'teacher' | 'student' | 'admin';
  created_at: string;
  updated_at: string;
}

class AuthService {
  private checkSupabase() {
    if (!isSupabaseConfigured()) {
      throw new Error('Supabase is not properly configured. Check your environment variables.');
    }
    
    if (!supabase) {
      throw new Error('Supabase client failed to initialize');
    }
    
    return supabase;
  }

  // Authentication methods
  async signUp(email: string, password: string, userData: { full_name?: string; role?: string } = {}) {
    console.log('AuthService: Signing up user:', email);
    const client = this.checkSupabase();
    
    try {
      const { data, error } = await client.auth.signUp({
        email,
        password,
        options: {
          data: {
            ...userData,
            email_confirm: true // Set to true if email verification is not required
          }
        }
      });
      
      if (error) {
        console.error('AuthService: Sign up error:', error.message);
        throw error;
      }
      
      console.log('AuthService: Sign up successful for:', email);
      return { data, error: null };
    } catch (error) {
      console.error('AuthService: Sign up failed:', error);
      return { 
        data: null, 
        error: error instanceof Error ? error : new Error('An unknown error occurred during sign up') 
      };
    }
  }

  async signIn(email: string, password: string) {
    console.log('AuthService: Signing in user:', email);
    const client = this.checkSupabase();
    
    try {
      const { data, error } = await client.auth.signInWithPassword({
        email,
        password
      });
      
      if (error) {
        console.error('AuthService: Sign in error:', error.message);
        throw error;
      }
      
      console.log('AuthService: Sign in successful for:', email);
      return { data, error: null };
    } catch (error) {
      console.error('AuthService: Sign in failed:', error);
      return { 
        data: null, 
        error: error instanceof Error ? error : new Error('An unknown error occurred during sign in') 
      };
    }
  }

  async signOut() {
    console.log('AuthService: Signing out user');
    const client = this.checkSupabase();
    
    try {
      const { error } = await client.auth.signOut();
      if (error) {
        console.error('AuthService: Sign out error:', error.message);
        throw error;
      }
      
      console.log('AuthService: Sign out successful');
      return { error: null };
    } catch (error) {
      console.error('AuthService: Sign out failed:', error);
      return { 
        error: error instanceof Error ? error : new Error('An unknown error occurred during sign out') 
      };
    }
  }

  async getCurrentUser() {
    console.log('AuthService: Getting current user');
    const client = this.checkSupabase();
    
    try {
      const { data: { user }, error } = await client.auth.getUser();
      
      if (error) {
        console.error('AuthService: Error getting current user:', error.message);
        throw error;
      }
      
      console.log('AuthService: Retrieved current user:', user?.email);
      return user;
    } catch (error) {
      console.error('AuthService: Failed to get current user:', error);
      throw error;
    }
  }

  async getProfile(userId: string) {
    console.log('AuthService: Getting profile for user:', userId);
    const client = this.checkSupabase();
    
    try {
      const { data, error } = await client
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();
        
      if (error) {
        console.error('AuthService: Error getting profile:', error.message);
        throw error;
      }
      
      console.log('AuthService: Retrieved profile for user:', userId);
      return data;
    } catch (error) {
      console.error('AuthService: Failed to get profile:', error);
      throw error;
    }
  }

  async updateProfile(userId: string, updates: Partial<Profile>) {
    console.log('AuthService: Updating profile for user:', userId);
    const client = this.checkSupabase();
    
    try {
      const { data, error } = await client
        .from('profiles')
        .update(updates)
        .eq('id', userId)
        .select()
        .single();
        
      if (error) {
        console.error('AuthService: Error updating profile:', error.message);
        throw error;
      }
      
      console.log('AuthService: Successfully updated profile for user:', userId);
      return data;
    } catch (error) {
      console.error('AuthService: Failed to update profile:', error);
      throw error;
    }
  }

  async resetPassword(email: string) {
    console.log('AuthService: Resetting password for email:', email);
    const client = this.checkSupabase();
    
    try {
      const { data, error } = await client.auth.resetPasswordForEmail(email);
      
      if (error) {
        console.error('AuthService: Error sending password reset email:', error.message);
        throw error;
      }
      
      console.log('AuthService: Password reset email sent to:', email);
      return { data, error: null };
    } catch (error) {
      console.error('AuthService: Failed to send password reset email:', error);
      return { 
        data: null, 
        error: error instanceof Error ? error : new Error('An unknown error occurred while sending password reset email') 
      };
    }
  }
}

export const authService = new AuthService();
