
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 142 76% 36%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: system-ui, -apple-system, sans-serif; /* Use system fonts for instant loading */
    background-color: #121212;
    overflow-x: hidden;
  }
  
  html {
    height: 100%;
    height: -webkit-fill-available;
  }
  
  body {
    min-height: 100vh;
    min-height: -webkit-fill-available;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-[#00E676] to-[#2979FF] bg-clip-text text-transparent;
  }
  
  .card-hover {
    @apply transition-transform duration-100 hover:scale-[1.01]; /* Reduced animation time */
  }
  
  .performance-optimized {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
  }
}

/* Minimal scrollbar */
::-webkit-scrollbar {
  width: 2px;
}

::-webkit-scrollbar-thumb {
  background: #424242;
  border-radius: 1px;
}

/* Disable all animations for better performance */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Critical CSS only - remove non-essential styles */
button:focus-visible,
a:focus-visible {
  outline: 1px solid #00E676;
  outline-offset: 1px;
}
