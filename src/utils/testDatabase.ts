import { supabase } from '@/integrations/supabase/client';

export async function testDatabaseConnection() {
  try {
    // Test subjects
    const { data: subjects, error: subjectsError } = await supabase
      .from('subjects')
      .select('*')
      .limit(1);

    if (subjectsError) throw subjectsError;
    console.log('✅ Subjects table test passed! Found:', subjects?.length || 0, 'subjects');

    // Test chapters
    const { data: chapters, error: chaptersError } = await supabase
      .from('chapters')
      .select('*')
      .limit(1);

    if (chaptersError) throw chaptersError;
    console.log('✅ Chapters table test passed! Found:', chapters?.length || 0, 'chapters');

    // Test study materials
    const { data: materials, error: materialsError } = await supabase
      .from('study_materials')
      .select('*')
      .limit(1);

    if (materialsError) throw materialsError;
    console.log('✅ Study materials table test passed! Found:', materials?.length || 0, 'materials');

    return true;
  } catch (error) {
    console.error('❌ Database test failed:', error);
    return false;
  }
}
