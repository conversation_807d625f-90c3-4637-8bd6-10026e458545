/**
 * Comprehensive application test utility
 */

import { supabase, isSupabaseConfigured } from '@/integrations/supabase/client';
import { authService } from '@/services/authService';
import { dataService } from '@/services/dataService';

export interface TestResult {
  test: string;
  success: boolean;
  message: string;
  error?: string;
}

export const runAppTests = async (): Promise<TestResult[]> => {
  const results: TestResult[] = [];

  // Test 1: Supabase Configuration
  try {
    const isConfigured = isSupabaseConfigured();
    results.push({
      test: 'Supabase Configuration',
      success: isConfigured,
      message: isConfigured ? 'Supabase is properly configured' : 'Supabase configuration missing'
    });
  } catch (error) {
    results.push({
      test: 'Supabase Configuration',
      success: false,
      message: 'Failed to check Supabase configuration',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }

  // Test 2: Database Connection
  try {
    if (supabase) {
      const { data, error } = await supabase.from('profiles').select('id').limit(1);
      results.push({
        test: 'Database Connection',
        success: !error,
        message: error ? `Database connection failed: ${error.message}` : 'Database connection successful'
      });
    } else {
      results.push({
        test: 'Database Connection',
        success: false,
        message: 'Supabase client not initialized'
      });
    }
  } catch (error) {
    results.push({
      test: 'Database Connection',
      success: false,
      message: 'Database connection test failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }

  // Test 3: Auth Service
  try {
    // Just test if the service is available
    const isAvailable = typeof authService.signIn === 'function';
    results.push({
      test: 'Auth Service',
      success: isAvailable,
      message: isAvailable ? 'Auth service is available' : 'Auth service not available'
    });
  } catch (error) {
    results.push({
      test: 'Auth Service',
      success: false,
      message: 'Auth service test failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }

  // Test 4: Data Service
  try {
    const isAvailable = typeof dataService.getStudyMaterials === 'function';
    results.push({
      test: 'Data Service',
      success: isAvailable,
      message: isAvailable ? 'Data service is available' : 'Data service not available'
    });
  } catch (error) {
    results.push({
      test: 'Data Service',
      success: false,
      message: 'Data service test failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }

  // Test 5: Database Tables
  if (supabase) {
    const tables = ['profiles', 'subjects', 'chapters', 'study_materials'];
    
    for (const table of tables) {
      try {
        const { error } = await supabase.from(table).select('*').limit(1);
        results.push({
          test: `Table: ${table}`,
          success: !error,
          message: error ? `Table ${table} error: ${error.message}` : `Table ${table} accessible`
        });
      } catch (error) {
        results.push({
          test: `Table: ${table}`,
          success: false,
          message: `Table ${table} test failed`,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  return results;
};

/**
 * Test teacher authentication flow
 */
export const testTeacherAuth = async (email: string = '<EMAIL>', password: string = 'teacher123'): Promise<TestResult> => {
  try {
    if (!isSupabaseConfigured()) {
      return {
        test: 'Teacher Authentication',
        success: false,
        message: 'Supabase not configured'
      };
    }

    // Try to sign in
    const { data, error } = await authService.signIn(email, password);
    
    if (error) {
      return {
        test: 'Teacher Authentication',
        success: false,
        message: `Authentication failed: ${error.message}`,
        error: error.message
      };
    }

    if (!data?.user) {
      return {
        test: 'Teacher Authentication',
        success: false,
        message: 'No user data returned'
      };
    }

    return {
      test: 'Teacher Authentication',
      success: true,
      message: `Authentication successful for ${email}`
    };
  } catch (error) {
    return {
      test: 'Teacher Authentication',
      success: false,
      message: 'Authentication test failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Test study materials loading
 */
export const testStudyMaterials = async (): Promise<TestResult> => {
  try {
    const { data, error } = await dataService.getStudyMaterials({ grade: 8 });
    
    if (error) {
      return {
        test: 'Study Materials',
        success: false,
        message: `Failed to load study materials: ${error.message}`,
        error: error.message
      };
    }

    return {
      test: 'Study Materials',
      success: true,
      message: `Successfully loaded ${data?.length || 0} study materials`
    };
  } catch (error) {
    return {
      test: 'Study Materials',
      success: false,
      message: 'Study materials test failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Generate a comprehensive test report
 */
export const generateTestReport = async (): Promise<{
  summary: { total: number; passed: number; failed: number };
  results: TestResult[];
}> => {
  console.log('Running comprehensive application tests...');
  
  const results = await runAppTests();
  
  // Add additional specific tests
  const teacherAuthResult = await testTeacherAuth();
  results.push(teacherAuthResult);
  
  const studyMaterialsResult = await testStudyMaterials();
  results.push(studyMaterialsResult);
  
  const passed = results.filter(r => r.success).length;
  const failed = results.length - passed;
  
  const summary = {
    total: results.length,
    passed,
    failed
  };
  
  console.log('Test Summary:', summary);
  console.log('Detailed Results:', results);
  
  return { summary, results };
};
