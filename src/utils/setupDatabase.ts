import { supabase } from '@/integrations/supabase/client';

/**
 * Setup database tables and initial data
 */
export const setupDatabase = async () => {
  if (!supabase) {
    throw new Error('Supabase client not initialized');
  }

  console.log('Setting up database...');

  try {
    // Test basic connection first
    const { data: testData, error: testError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);

    if (testError) {
      console.error('Database connection test failed:', testError);
      return { success: false, error: testError.message };
    }

    console.log('Database connection successful');

    // Check if subjects table has data
    const { data: subjects, error: subjectsError } = await supabase
      .from('subjects')
      .select('*')
      .limit(1);

    if (subjectsError) {
      console.error('Subjects table error:', subjectsError);
      return { success: false, error: subjectsError.message };
    }

    // If no subjects exist, create some sample data
    if (!subjects || subjects.length === 0) {
      console.log('No subjects found, creating sample data...');
      await createSampleData();
    }

    return { success: true, message: 'Database setup completed successfully' };
  } catch (error) {
    console.error('Database setup failed:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
};

/**
 * Create sample subjects and chapters
 */
const createSampleData = async () => {
  if (!supabase) return;

  // Sample subjects for different grades
  const sampleSubjects = [
    { name: 'Mathematics', description: 'Class 8 Mathematics curriculum', grade: 8, icon: '🧮', color: '#2979FF' },
    { name: 'Science', description: 'Class 8 Science curriculum', grade: 8, icon: '🔬', color: '#00E676' },
    { name: 'English', description: 'Class 8 English curriculum', grade: 8, icon: '📖', color: '#FFA726' },
    { name: 'Mathematics', description: 'Class 9 Mathematics curriculum', grade: 9, icon: '🧮', color: '#2979FF' },
    { name: 'Science', description: 'Class 9 Science curriculum', grade: 9, icon: '🔬', color: '#00E676' },
    { name: 'English', description: 'Class 9 English curriculum', grade: 9, icon: '📖', color: '#FFA726' },
    { name: 'Mathematics', description: 'Class 10 Mathematics curriculum', grade: 10, icon: '🧮', color: '#2979FF' },
    { name: 'Science', description: 'Class 10 Science curriculum', grade: 10, icon: '🔬', color: '#00E676' },
    { name: 'English', description: 'Class 10 English curriculum', grade: 10, icon: '📖', color: '#FFA726' },
  ];

  try {
    // Insert subjects
    const { data: insertedSubjects, error: subjectsError } = await supabase
      .from('subjects')
      .insert(sampleSubjects)
      .select();

    if (subjectsError) {
      console.error('Error creating sample subjects:', subjectsError);
      return;
    }

    console.log('Sample subjects created:', insertedSubjects?.length);

    // Create sample chapters for Mathematics Class 8
    const mathSubject = insertedSubjects?.find(s => s.name === 'Mathematics' && s.grade === 8);
    if (mathSubject) {
      const sampleChapters = [
        { name: 'Chapter 1: Rational Numbers', description: 'Understanding rational numbers and their operations', subject_id: mathSubject.id, order_index: 1 },
        { name: 'Chapter 2: Linear Equations in One Variable', description: 'Solving linear equations', subject_id: mathSubject.id, order_index: 2 },
        { name: 'Chapter 3: Understanding Quadrilaterals', description: 'Properties of quadrilaterals', subject_id: mathSubject.id, order_index: 3 },
        { name: 'Chapter 4: Practical Geometry', description: 'Constructing geometric figures', subject_id: mathSubject.id, order_index: 4 },
      ];

      const { error: chaptersError } = await supabase
        .from('chapters')
        .insert(sampleChapters);

      if (chaptersError) {
        console.error('Error creating sample chapters:', chaptersError);
      } else {
        console.log('Sample chapters created for Mathematics Class 8');
      }
    }

  } catch (error) {
    console.error('Error creating sample data:', error);
  }
};

/**
 * Check database health
 */
export const checkDatabaseHealth = async () => {
  if (!supabase) {
    return { healthy: false, error: 'Supabase client not initialized' };
  }

  try {
    // Test each table
    const tests = [
      { table: 'profiles', query: supabase.from('profiles').select('id').limit(1) },
      { table: 'subjects', query: supabase.from('subjects').select('id').limit(1) },
      { table: 'chapters', query: supabase.from('chapters').select('id').limit(1) },
      { table: 'study_materials', query: supabase.from('study_materials').select('id').limit(1) },
    ];

    const results = await Promise.allSettled(
      tests.map(async ({ table, query }) => {
        const { error } = await query;
        return { table, success: !error, error: error?.message };
      })
    );

    const healthStatus = results.map((result, index) => ({
      table: tests[index].table,
      ...(result.status === 'fulfilled' ? result.value : { success: false, error: 'Promise rejected' })
    }));

    const allHealthy = healthStatus.every(status => status.success);

    return {
      healthy: allHealthy,
      tables: healthStatus,
      error: allHealthy ? null : 'Some tables are not accessible'
    };
  } catch (error) {
    return {
      healthy: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};
