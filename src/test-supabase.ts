import { supabase, isSupabaseConfigured } from '@/integrations/supabase/client';

console.log('Testing Supabase connection...');
console.log('isSupabaseConfigured:', isSupabaseConfigured());
console.log('Supabase URL:', import.meta.env.VITE_SUPABASE_URL);
console.log('Supabase Anon Key:', import.meta.env.VITE_SUPABASE_ANON_KEY ? '***' : 'Not set');

async function testConnection() {
  try {
    if (!isSupabaseConfigured() || !supabase) {
      console.error('Supabase is not properly configured');
      return;
    }

    console.log('Testing Supabase connection...');
    const { data, error } = await supabase.from('subjects').select('*').limit(1);

    if (error) {
      console.error('Supabase query error:', error);
    } else {
      console.log('Supabase query successful:', data);
    }
  } catch (error) {
    console.error('Error testing Supabase connection:', error);
  }
}

// Run the test
testConnection();
