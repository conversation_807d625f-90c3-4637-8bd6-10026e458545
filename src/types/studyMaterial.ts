export type StudyMaterialType = 'textbook' | 'video' | 'summary' | 'ppt' | 'quiz';

export interface StudyMaterialBase {
  id: string;
  title: string;
  description?: string;
  type: StudyMaterialType;
  created_at: string;
  updated_at: string;
  is_public: boolean;
  teacher_id: string;
}

export interface StudyMaterialWithRelations extends StudyMaterialBase {
  subjects?: {
    name?: string;
    grade?: number;
  };
  profiles?: {
    full_name?: string;
  };
  url?: string;
  file_path?: string;
  subject_id?: string;
  chapter_id?: string;
  grade?: number;
}

export interface LocalStudyMaterial {
  pdfUrl?: string;
  videoUrl?: string;
  practiceQuestionsUrl?: string;
  quizUrl?: string;
  description?: string;
}

export interface StudyMaterialResponse {
  data: StudyMaterialWithRelations[] | null;
  error: Error | null;
}
