
import React, { useState, useEffect } from 'react';
import { Arrow<PERSON>eft, Clock, Users, Trophy, RefreshCw, BookOpen, FileText, Video, FileSliders } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { subjects, SubjectName } from '@/data/subjects';
import { getStudyMaterial as getLocalStudyMaterial } from '@/data/studyMaterials';
import { StudyMaterial as DatabaseStudyMaterial } from '@/services/dataService';
import { dataService } from '@/services/dataService';
import { supabaseService } from '@/services/supabaseService';
import { toast } from '@/hooks/use-toast';
import { isSupabaseConfigured } from '@/integrations/supabase/client';
import PDFViewer from '@/components/PDFViewer';

interface ChapterStudyMaterialProps {
  subject: string;
  chapter: string;
  selectedGrade: number;
  onBack: () => void;
  onError?: (error: Error) => void;
}

const ChapterStudyMaterial = ({ subject, chapter, selectedGrade, onBack }: ChapterStudyMaterialProps) => {
  const [studyMaterials, setStudyMaterials] = useState<Array<DatabaseStudyMaterial & { subjects?: { name?: string; grade?: number }; profiles?: { full_name?: string } }>>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('textbook');
  const [hasSupabaseError, setHasSupabaseError] = useState(false);
  const [showError, setShowError] = useState(false);
  const [hasPDFError, setHasPDFError] = useState(false);

  const subjectData = subjects[subject];
  const localStudyMaterial = getLocalStudyMaterial(subject, selectedGrade, chapter) || {};
  const hasLocalPDF = localStudyMaterial?.pdfUrl;
  const hasSupabasePDF = studyMaterials.some(mat => mat.type === 'textbook' && mat.url);

  useEffect(() => {
    loadStudyMaterials();
  }, [subject, chapter, selectedGrade]);

  const loadStudyMaterials = async () => {
    setIsLoading(true);
    try {
      // Check if Supabase is properly configured
      if (!isSupabaseConfigured()) {
        console.warn('Supabase is not configured. Falling back to local data.');
        setStudyMaterials([]);
        return;
      }

      // First try to load from Supabase
      if (supabaseService) {
        const { data, error } = await dataService.getStudyMaterials({
          grade: selectedGrade,
          subject_id: subjectData?.id
        });

        if (error) {
          console.error('Error loading study materials:', error);
          if (error.code === '42P01') {
            console.warn('Database tables not found. Falling back to local data.');
          } else {
            toast({
              title: 'Error loading content',
              description: 'Falling back to local data. Please check your internet connection.',
              variant: 'destructive'
            });
          }
          setHasSupabaseError(true);
          setShowError(true);
          return;
        }

        if (data) {
          // Filter materials for the current chapter
          const chapterMaterials = data.filter(mat => 
            mat.chapter_id && mat.chapter_id === chapter
          );
          
          setStudyMaterials(chapterMaterials.map(material => ({
            ...material,
            created_at: material.created_at || new Date().toISOString(),
            updated_at: material.updated_at || new Date().toISOString(),
          })));
        }
      } else {
        // If no Supabase client, just use local data
        setShowError(false);
      }
    } catch (error) {
      console.error('Failed to load study materials:', error);
      setHasSupabaseError(true);
      setShowError(true);
      if (error instanceof Error) {
        console.error('Error details:', {
          message: error.message,
          stack: error.stack
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  // If there's an error but we have local data, show the local data
  if (hasSupabaseError && localStudyMaterial) {
    return (
      <div className="min-h-screen bg-[#121212] flex items-center justify-center p-4">
        <div className="text-center max-w-md">
          <div className="text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-bold text-white mb-4">Database Connection Error</h2>
          <p className="text-[#E0E0E0] mb-6">
            We're having trouble connecting to the database. Showing local content instead.
          </p>
          <div className="space-y-2 text-[#E0E0E0] mb-6">
            <div className="flex items-center gap-2">
              <span className="text-[#00E676]">1.</span>
              <span>Make sure you have internet connection</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-[#00E676]">2.</span>
              <span>Check if the database is properly configured</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-[#00E676]">3.</span>
              <span>Try refreshing the page</span>
            </div>
          </div>
          <Button 
            onClick={() => {
              setHasSupabaseError(false);
              loadStudyMaterials();
            }}
            className="bg-[#00E676] text-black hover:bg-[#00E676]/90"
          >
            Try Again
          </Button>
          <Button 
            onClick={onBack}
            variant="outline"
            className="mt-4"
          >
            Back to Subjects
          </Button>
        </div>
      </div>
    );
  }

  // If we have local data, show it regardless of Supabase status
  if (localStudyMaterial) {
    return (
      <div className="min-h-screen bg-[#121212] flex items-center justify-center p-4">
        <div className="max-w-4xl w-full">
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <Button 
                onClick={onBack}
                variant="outline"
                className="text-[#00E676] hover:bg-[#00E676]/10"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to {subject}
              </Button>
              <h2 className="text-2xl font-bold bg-gradient-to-r from-[#00E676] to-[#2979FF] bg-clip-text text-transparent">
                {chapter}
              </h2>
            </div>
            <p className="text-[#CCCCCC] mt-2">
              {localStudyMaterial.description || `Chapter ${chapter.split(' ')[1]} of ${subject} for Class ${selectedGrade}`}
            </p>
          </div>

          {/* Content Tabs */}
          <Tabs defaultValue="textbook" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="textbook" className="data-[state=active]:bg-[#00E676] data-[state=active]:text-black">
                <BookOpen className="h-4 w-4 mr-2" />
                Textbook
              </TabsTrigger>
              <TabsTrigger value="videos" className="data-[state=active]:bg-[#2979FF] data-[state=active]:text-black">
                <Video className="h-4 w-4 mr-2" />
                Videos
              </TabsTrigger>
              <TabsTrigger value="summaries" className="data-[state=active]:bg-[#00E676] data-[state=active]:text-black">
                <FileText className="h-4 w-4 mr-2" />
                Summaries
              </TabsTrigger>
              <TabsTrigger value="quizzes" className="data-[state=active]:bg-[#2979FF] data-[state=active]:text-black">
                <Trophy className="h-4 w-4 mr-2" />
                Quizzes
              </TabsTrigger>
            </TabsList>
            <TabsContent value="textbook" className="mt-4">
              <div className="space-y-4">
                {hasLocalPDF && (
                  <PDFViewer 
                    pdfUrl={localStudyMaterial.pdfUrl}
                    title={chapter}
                    onError={() => setHasPDFError(true)}
                  />
                )}
                {hasSupabasePDF && (
                  <PDFViewer 
                    pdfUrl={studyMaterials.find(mat => mat.type === 'textbook' && mat.url)?.url || ''}
                    title={chapter}
                    onError={() => setHasPDFError(true)}
                  />
                )}
                {(!hasLocalPDF && !hasSupabasePDF) && (
                  <div className="text-center py-8">
                    <p className="text-[#CCCCCC] mb-2">No textbook available for this chapter</p>
                    <p className="text-[#999999] text-sm">
                      Try the videos or summaries tab for alternative content
                    </p>
                  </div>
                )}
                {hasPDFError && (
                  <div className="text-center py-8">
                    <p className="text-[#FF6B6B] mb-2">PDF loading failed</p>
                    <p className="text-[#999999] text-sm">
                      Try refreshing the page or check your internet connection
                    </p>
                  </div>
                )}
              </div>
            </TabsContent>
            <TabsContent value="videos" className="mt-4">
              <div className="space-y-4">
                {localStudyMaterial.videoUrl ? (
                  <div key="video" className="aspect-video">
                    <iframe 
                      src={localStudyMaterial.videoUrl} 
                      className="w-full h-full rounded-lg"
                      allowFullScreen
                    />
                  </div>
                ) : null}
              </div>
            </TabsContent>
            <TabsContent value="summaries" className="mt-4">
              <div className="space-y-4">
                {localStudyMaterial.practiceQuestionsUrl && (
                  <div className="prose prose-invert max-w-none">
                    <p>Practice Questions Available</p>
                    <Button 
                      variant="outline" 
                      onClick={() => window.open(localStudyMaterial.practiceQuestionsUrl, '_blank')}
                      className="mt-2"
                    >
                      Open Practice Questions
                    </Button>
                  </div>
                )}
                {localStudyMaterial.description && (
                  <div className="prose prose-invert max-w-none">
                    <p>{localStudyMaterial.description}</p>
                  </div>
                )}
              </div>
            </TabsContent>
            <TabsContent value="quizzes" className="mt-4">
              <div className="space-y-4">
                {localStudyMaterial.quizUrl && (
                  <div key="quiz">
                    <h3 className="text-lg font-semibold text-[#00E676] mb-2">Quiz Available</h3>
                    <Button 
                      variant="outline" 
                      onClick={() => window.open(localStudyMaterial.quizUrl, '_blank')}
                      className="mt-2"
                    >
                      Take Quiz
                    </Button>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    );
  }

  // Categorize materials by type
  const textbookMaterials = studyMaterials.filter(m => m.type === 'textbook');
  const videoMaterials = studyMaterials.filter(m => m.type === 'video');
  const summaryMaterials = studyMaterials.filter(m => m.type === 'summary');
  const pptMaterials = studyMaterials.filter(m => m.type === 'ppt');
  const quizMaterials = studyMaterials.filter(m => m.type === 'quiz');

  // Get the primary textbook for theory tab (prioritize teacher uploads)
  const primaryTextbook = textbookMaterials[0] || (localStudyMaterial?.pdfUrl ? {
    id: 'local-pdf',
    title: `${chapter} - Textbook`,
    url: localStudyMaterial.pdfUrl,
    type: 'textbook' as const,
    teacher_id: '',
    is_public: true,
    created_at: '',
    updated_at: ''
  } : null);

  const renderMaterialCard = (material: StudyMaterial, icon: string, color: string) => (
    <Card key={material.id} className="bg-[#2C2C2C]/50 backdrop-blur-sm border-[#424242] hover:bg-[#2C2C2C]/70 transition-all duration-300 touch-manipulation">
      <CardHeader className="pb-3">
        <CardTitle className={`text-${color} text-base md:text-lg flex items-center gap-2 flex-wrap`}>
          <span className="text-xl">{icon}</span>
          <span className="flex-1 min-w-0 truncate">{material.title}</span>
          <Badge variant="secondary" className={`bg-${color}/20 text-${color} border-${color}/30 text-xs`}>
            {material.type.toUpperCase()}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        {material.description && (
          <p className="text-[#E0E0E0] mb-3 text-sm md:text-base line-clamp-2">{material.description}</p>
        )}
        <div className="flex items-center gap-2 text-xs text-[#666666] mb-4">
          <Clock className="h-3 w-3 flex-shrink-0" />
          <span>Added {new Date(material.created_at).toLocaleDateString()}</span>
        </div>
        <Button 
          onClick={() => {
            if (material.url) {
              window.open(material.url, '_blank');
            } else if (material.file_path) {
              // Use Supabase storage URL for uploaded files
              if (material.file_path) {
                const fileUrl = supabaseService.getFileUrl('study-materials', material.file_path);
                window.open(fileUrl, '_blank');
              }
            }
          }}
          className={`w-full bg-${color} text-black hover:bg-${color}/90 font-medium h-11 md:h-12 touch-manipulation`}
        >
          {material.type === 'video' ? 'Watch Video' : 
           material.type === 'textbook' ? 'View Textbook' :
           material.type === 'summary' ? 'Read Summary' :
           material.type === 'ppt' ? 'View Presentation' :
           material.type === 'quiz' ? 'Take Quiz' :
           'Open Content'}
        </Button>
      </CardContent>
    </Card>
  );

  const renderComingSoonCard = (title: string, description: string, icon: string, color: string, estimatedTime: string) => (
    <Card className="bg-[#2C2C2C]/50 backdrop-blur-sm border-[#424242] hover:bg-[#2C2C2C]/70 transition-all duration-300">
      <CardHeader className="pb-3">
        <CardTitle className={`text-${color} text-base md:text-lg flex items-center gap-2`}>
          <span className="text-xl">{icon}</span>
          <span className="flex-1">{title}</span>
          <Badge variant="secondary" className="bg-[#666666]/20 text-[#666666] border-[#666666]/30 text-xs">
            Coming Soon
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-[#E0E0E0] mb-3 text-sm md:text-base">{description}</p>
        <div className="flex items-center gap-2 text-xs text-[#666666] mb-4">
          <Clock className="h-3 w-3" />
          <span>{estimatedTime}</span>
        </div>
        <Button disabled className="w-full bg-[#666666] text-[#CCCCCC] font-medium h-11 md:h-12">
          Coming Soon
        </Button>
      </CardContent>
    </Card>
  );

  if (isLoading) {
    return (
      <div className="p-4 md:p-6 min-h-screen bg-gradient-to-br from-[#121212] to-[#1A1A1A] flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-12 w-12 animate-spin mx-auto mb-4 text-[#00E676]" />
          <p className="text-[#E0E0E0] text-lg">Loading study materials...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 md:p-6 min-h-screen bg-gradient-to-br from-[#121212] to-[#1A1A1A]">
      <div className="max-w-7xl mx-auto">
        {/* Header - Tablet Optimized */}
        <div className="flex flex-col md:flex-row items-start md:items-center gap-4 mb-6">
          <Button 
            variant="ghost" 
            onClick={onBack}
            className="text-[#E0E0E0] hover:text-[#00E676] hover:bg-[#00E676]/10 transition-all duration-200 touch-manipulation h-12 px-4"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back to Subjects
          </Button>
          <Button
            variant="outline"
            onClick={loadStudyMaterials}
            className="border-[#2979FF] text-[#2979FF] hover:bg-[#2979FF] hover:text-white h-12 px-4 touch-manipulation"
          >
            <RefreshCw className="h-5 w-5 mr-2" />
            Refresh Content
          </Button>
        </div>
        
        <Card className="bg-[#1A1A1A]/80 backdrop-blur-md border-[#2C2C2C] shadow-2xl">
          <CardHeader className="pb-4">
            <CardTitle className="text-white flex flex-col md:flex-row md:items-center gap-4">
              <div className={`p-3 rounded-xl bg-gradient-to-r ${subjectData.gradient} flex-shrink-0`}>
                <span className="text-2xl">{subjectData.icon}</span>
              </div>
              <div className="flex-1">
                <h1 className="text-xl md:text-2xl lg:text-3xl bg-gradient-to-r from-white to-[#E0E0E0] bg-clip-text text-transparent leading-tight">
                  {subject} - {chapter}
                </h1>
                <p className="text-[#E0E0E0] text-sm md:text-base font-normal mt-1">
                  Class {selectedGrade} Study Material ({studyMaterials.length} items available)
                </p>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-5 bg-[#2C2C2C] border border-[#424242] mb-6 h-auto">
                <TabsTrigger 
                  value="textbook" 
                  className="data-[state=active]:bg-[#00E676] data-[state=active]:text-black text-[#E0E0E0] h-12 md:h-10 text-sm touch-manipulation"
                >
                  <div className="flex items-center gap-1">
                    <BookOpen className="h-4 w-4" />
                    <span className="hidden md:inline">Textbook</span>
                  </div>
                </TabsTrigger>
                <TabsTrigger 
                  value="videos" 
                  className="data-[state=active]:bg-[#2979FF] data-[state=active]:text-white text-[#E0E0E0] h-12 md:h-10 text-sm touch-manipulation"
                >
                  <div className="flex items-center gap-1">
                    <Video className="h-4 w-4" />
                    <span className="hidden md:inline">Videos</span>
                  </div>
                </TabsTrigger>
                <TabsTrigger 
                  value="summary" 
                  className="data-[state=active]:bg-[#FFA726] data-[state=active]:text-black text-[#E0E0E0] h-12 md:h-10 text-sm touch-manipulation"
                >
                  <div className="flex items-center gap-1">
                    <FileText className="h-4 w-4" />
                    <span className="hidden md:inline">Summary</span>
                  </div>
                </TabsTrigger>
                <TabsTrigger 
                  value="ppt" 
                  className="data-[state=active]:bg-[#FF7043] data-[state=active]:text-white text-[#E0E0E0] h-12 md:h-10 text-sm touch-manipulation"
                >
                  <div className="flex items-center gap-1">
                    <FileSliders className="h-4 w-4" />
                    <span className="hidden md:inline">PPT</span>
                  </div>
                </TabsTrigger>
                <TabsTrigger 
                  value="quiz" 
                  className="data-[state=active]:bg-[#E91E63] data-[state=active]:text-white text-[#E0E0E0] h-12 md:h-10 text-sm touch-manipulation"
                >
                  <div className="flex items-center gap-1">
                    <Trophy className="h-4 w-4" />
                    <span className="hidden md:inline">Quiz</span>
                  </div>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="textbook" className="space-y-6">
                {primaryTextbook ? (
                  <Card className="bg-[#2C2C2C]/50 backdrop-blur-sm border-[#424242]">
                    <CardHeader>
                      <CardTitle className="text-[#00E676] text-lg md:text-xl flex items-center gap-2">
                        <BookOpen className="h-5 w-5" />
                        {primaryTextbook.title}
                        <Badge variant="secondary" className="bg-[#00E676]/20 text-[#00E676] border-[#00E676]/30">
                          TEXTBOOK
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <PDFViewer 
                        pdfUrl={primaryTextbook.url || supabaseService.getFileUrl('study-materials', primaryTextbook.file_path || '')} 
                        title={primaryTextbook.title}
                      />
                    </CardContent>
                  </Card>
                ) : (
                  <div className="text-center py-12">
                    <div className="text-4xl md:text-6xl mb-4"><BookOpen className="h-16 w-16 mx-auto text-[#666666]" /></div>
                    <h3 className="text-lg md:text-xl text-[#E0E0E0] mb-2">No textbook materials yet</h3>
                    <p className="text-[#666666] text-sm md:text-base">Textbook materials for {chapter} will be uploaded by teachers soon.</p>
                  </div>
                )}

                {/* Additional textbook materials */}
                {textbookMaterials.length > 1 && (
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                    {textbookMaterials.slice(1).map(material => 
                      renderMaterialCard(material, '📚', '[#00E676]')
                    )}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="videos" className="space-y-6">
                {videoMaterials.length > 0 ? (
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                    {videoMaterials.map(material => 
                      renderMaterialCard(material, '🎥', '[#2979FF]')
                    )}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="text-4xl md:text-6xl mb-4"><Video className="h-16 w-16 mx-auto text-[#666666]" /></div>
                    <h3 className="text-lg md:text-xl text-[#E0E0E0] mb-2">No video materials yet</h3>
                    <p className="text-[#666666] text-sm md:text-base">Video lectures for {chapter} will be uploaded by teachers soon.</p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="summary" className="space-y-6">
                {summaryMaterials.length > 0 ? (
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                    {summaryMaterials.map(material => 
                      renderMaterialCard(material, '📝', '[#FFA726]')
                    )}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="text-4xl md:text-6xl mb-4"><FileText className="h-16 w-16 mx-auto text-[#666666]" /></div>
                    <h3 className="text-lg md:text-xl text-[#E0E0E0] mb-2">No summary materials yet</h3>
                    <p className="text-[#666666] text-sm md:text-base">Summary notes for {chapter} will be uploaded by teachers soon.</p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="ppt" className="space-y-6">
                {pptMaterials.length > 0 ? (
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                    {pptMaterials.map(material => 
                      renderMaterialCard(material, '📊', '[#FF7043]')
                    )}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="text-4xl md:text-6xl mb-4"><FileSliders className="h-16 w-16 mx-auto text-[#666666]" /></div>
                    <h3 className="text-lg md:text-xl text-[#E0E0E0] mb-2">No presentation materials yet</h3>
                    <p className="text-[#666666] text-sm md:text-base">PowerPoint presentations for {chapter} will be uploaded by teachers soon.</p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="quiz" className="space-y-6">
                {quizMaterials.length > 0 ? (
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                    {quizMaterials.map(material => 
                      renderMaterialCard(material, '🏆', '[#E91E63]')
                    )}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="text-4xl md:text-6xl mb-4"><Trophy className="h-16 w-16 mx-auto text-[#666666]" /></div>
                    <h3 className="text-lg md:text-xl text-[#E0E0E0] mb-2">No quizzes available yet</h3>
                    <p className="text-[#666666] text-sm md:text-base">Quizzes and assessments for {chapter} will be created by teachers soon.</p>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ChapterStudyMaterial;
