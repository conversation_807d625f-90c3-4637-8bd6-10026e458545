import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabaseClient, isSupabaseConfigured } from '@/integrations/supabase/client';
import { authService, type Profile } from '@/services/authService';

// Type guard to check if a value is a valid role type
const isValidRole = (role: unknown): role is Profile['role'] => {
  return typeof role === 'string' && ['teacher', 'student', 'admin'].includes(role);
};

// Helper function to safely convert a raw profile object to a Profile type
const toSafeProfile = (rawProfile: any): Profile | null => {
  if (!rawProfile) return null;
  
  // Ensure we have a proper object
  const profileData = typeof rawProfile === 'object' && rawProfile !== null 
    ? rawProfile as Record<string, unknown> 
    : {};
  
  // Create a safe profile object with defaults
  return {
    id: typeof profileData.id === 'string' ? profileData.id : '',
    email: typeof profileData.email === 'string' ? profileData.email : '',
    full_name: typeof profileData.full_name === 'string' ? profileData.full_name : '',
    role: isValidRole(profileData.role) ? profileData.role : 'student',
    created_at: typeof profileData.created_at === 'string' ? profileData.created_at : new Date().toISOString(),
    updated_at: typeof profileData.updated_at === 'string' ? profileData.updated_at : new Date().toISOString()
  };
};

// Define response types for better type safety
interface AuthResponse<T = any> {
  data: T | null;
  error: Error | null;
}

interface SignInResponse extends AuthResponse<{ user: User | null }> {}
interface SignUpResponse extends AuthResponse<{ user: User | null }> {}

interface AuthContextType {
  user: User | null;
  profile: Profile | null;
  session: Session | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<SignInResponse>;
  signUp: (email: string, password: string, userData?: { full_name?: string; role?: Profile['role'] }) => Promise<SignUpResponse>;
  signOut: () => Promise<void>;
}

// Helper function to safely parse error messages
const getErrorMessage = (error: unknown): string => {
  if (error instanceof Error) {
    return error.message;
  }
  return String(error);
};

const AuthContext = createContext<AuthContextType | null>(null);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!isSupabaseConfigured()) {
      console.error('Supabase is not configured correctly');
      setLoading(false);
      return;
    }

    const session = supabaseClient.auth.getSession();
    setSession(session.data.session ?? null);
    setUser(session.data.session?.user ?? null);

    const { data: authListener } = supabaseClient.auth.onAuthStateChange((_event, session) => {
      setSession(session ?? null);
      setUser(session?.user ?? null);
    });

    return () => {
      authListener.subscription.unsubscribe();
    };
  }, []);

  const signIn = useCallback(async (email: string, password: string): Promise<SignInResponse> => {
    try {
      const { data, error } = await supabaseClient.auth.signInWithPassword({ email, password });
      if (error) throw error;
      setUser(data.user);
      setSession(data.session ?? null);
      return { data, error: null };
    } catch (error: any) {
      return { data: null, error };
    }
  }, []);

  const signUp = useCallback(async (email: string, password: string, userData?: { full_name?: string; role?: Profile['role'] }): Promise<SignUpResponse> => {
    try {
      const { data, error } = await supabaseClient.auth.signUp({ email, password });
      if (error) throw error;
      if (data.user && userData) {
        const rawProfile = await authService.createProfile(data.user.id, userData);
        setProfile(toSafeProfile(rawProfile));
      }
      return { data, error: null };
    } catch (error: any) {
      return { data: null, error };
    }
  }, []);

  const signOut = useCallback(async (): Promise<void> => {
    await supabaseClient.auth.signOut();
    setUser(null);
    setSession(null);
    setProfile(null);
  }, []);

  return (
    <AuthContext.Provider value={{ user, profile, session, loading, signIn, signUp, signOut }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
