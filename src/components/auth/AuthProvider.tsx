
import React, { createContext, useContext, useEffect, useState, useCallback, useMemo } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase, isSupabaseConfigured } from '@/integrations/supabase/client';
import { authService, type Profile } from '@/services/authService';

// Type guard to check if a value is a valid role type
const isValidRole = (role: unknown): role is Profile['role'] => {
  return typeof role === 'string' && ['teacher', 'student', 'admin'].includes(role);
};

// Helper function to safely convert a raw profile object to a Profile type
const toProfile = (rawProfile: any): Profile | null => {
  if (!rawProfile) return null;
  
  // Ensure the role is valid, default to 'student' if not
  const role = isValidRole(rawProfile.role) ? rawProfile.role : 'student';
  
  return {
    ...rawProfile,
    role,
    created_at: rawProfile.created_at || new Date().toISOString(),
    updated_at: rawProfile.updated_at || new Date().toISOString()
  };
};

// Define response types for better type safety

// Helper function to safely create a Profile object with proper typing
const createProfile = (data: any): Profile | null => {
  if (!data) return null;
  
  const role = isValidRole(data.role) ? data.role : 'student';
  
  return {
    id: data.id || '',
    email: data.email || '',
    full_name: data.full_name || '',
    role,
    created_at: data.created_at || new Date().toISOString(),
    updated_at: data.updated_at || new Date().toISOString()
  } as Profile;
};

// Define response types for better type safety
interface AuthResponse<T = any> {
  data: T | null;
  error: Error | null;
}

interface SignInResponse extends AuthResponse<{ user: User | null }> {}
interface SignUpResponse extends AuthResponse<{ user: User | null }> {}

interface AuthContextType {
  user: User | null;
  profile: Profile | null;
  session: Session | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<SignInResponse>;
  signUp: (email: string, password: string, userData?: { full_name?: string; role?: Profile['role'] }) => Promise<SignUpResponse>;
  signOut: () => Promise<void>;
}

// Helper function to safely parse error messages
const getErrorMessage = (error: unknown): string => {
  if (error instanceof Error) {
    return error.message;
  }
  return String(error);
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Helper function to safely set profile with type checking
export const useSafeProfile = (initialState: Profile | null = null): [Profile | null, (profile: Profile | null) => void] => {
  const [profile, setProfile] = useState<Profile | null>(initialState);

  const safeSetProfile = useCallback((newProfile: Profile | null) => {
    if (newProfile) {
      // Ensure role is one of the allowed values
      if (!isValidRole(newProfile.role)) {
        console.warn('Invalid role provided, defaulting to student');
        const safeProfile = {
          ...newProfile,
          role: 'student' as const,
          created_at: newProfile.created_at || new Date().toISOString(),
          updated_at: newProfile.updated_at || new Date().toISOString()
        };
        setProfile(safeProfile);
      } else {
        setProfile({
          ...newProfile,
          created_at: newProfile.created_at || new Date().toISOString(),
          updated_at: newProfile.updated_at || new Date().toISOString()
        });
      }
    } else {
      setProfile(null);
    }
  }, [setProfile]);

  return [profile, safeSetProfile] as const;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  // Handle auth state changes
  useEffect(() => {
    if (!supabase) {
      console.error('Supabase client is not initialized');
      return;
    }

    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session: initialSession }, error } = await supabase.auth.getSession();
        if (error) throw error;
        
        if (initialSession) {
          setUser(initialSession.user);
          setSession(initialSession);
        }
      } catch (error) {
        console.error('Error getting initial session:', error);
      }
    };

    getInitialSession();

    // Set up auth state change listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user ?? null);
      setSession(session ?? null);
    });

    return () => {
      if (subscription) {
        subscription.unsubscribe();
      }
    };
  }, []);

  // Fetch profile when user changes
  useEffect(() => {
    if (!user || !supabase) return;

    const fetchProfile = async () => {
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (error) throw error;
        
        // Convert raw profile data to Profile type
        const profileData = createProfile(data);
        if (profileData) {
          setProfile(profileData);
        } else {
          console.error('Invalid profile data received');
          setProfile(null);
        }
      } catch (error) {
        console.error('Error fetching profile:', error);
        setProfile(null);
      }
    };

    fetchProfile();
  }, [user]);

  // Safe profile setter that handles type checking and validation
  const setProfileSafely = useCallback((profileData: unknown) => {
    if (!profileData) {
      console.error('No profile data provided to setProfileSafely');
      setProfile(null);
      return;
    }
    
    try {
      const profile = createProfile(profileData);
      if (profile) {
        setProfile(profile);
      } else {
        console.error('Failed to create profile from data:', profileData);
        setProfile(null);
      }
    } catch (error) {
      console.error('Error in setProfileSafely:', error);
      setProfile(null);
    }
  }, []);

  // Define signOut
  const signOut = useCallback(async (): Promise<void> => {
    console.log('AuthProvider: Attempting to sign out');
    
    // Clear local state first to ensure UI updates immediately
    setUser(null);
    setProfile(null);
    setSession(null);
    
    if (!isSupabaseConfigured() || !supabase) {
      console.log('AuthProvider: Sign out completed (no active session)');
      return;
    }
    
    try {
      const { error } = await authService.signOut();
      
      if (error) {
        console.error('AuthProvider: Sign out failed:', error.message);
        throw error;
      }
      
      console.log('AuthProvider: Sign out successful');
    } catch (error) {
      const errorMessage = getErrorMessage(error);
      console.error('AuthProvider: Error during sign out:', errorMessage);
      // Even if the server sign out fails, we've already cleared the local state
      throw new Error(`Sign out failed: ${errorMessage}`);
    }
  }, [setProfile]);

  // Handle auth state changes
  useEffect(() => {
    // Check if Supabase is properly configured
    if (!isSupabaseConfigured()) {
      console.error('Supabase is not properly configured. Authentication features will be disabled.');
      setLoading(false);
      return;
    }

    // Verify Supabase client is initialized
    if (!supabase) {
      console.error('Supabase client failed to initialize. Authentication features will be disabled.');
      setLoading(false);
      return;
    }

    // Get initial session
    const getInitialSession = async () => {
      try {
        console.log('AuthProvider: Getting initial session...');
        const { data: { session: initialSession }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('AuthProvider: Error getting initial session:', error.message);
          setLoading(false);
          return;
        }
        
        // Validate session security
        if (initialSession) {
          const now = new Date();
          const sessionTime = new Date(initialSession.user.created_at);
          const sessionAge = now.getTime() - sessionTime.getTime();
          
          // Log potential security issues
          if (sessionAge > 24 * 60 * 60 * 1000) { // 24 hours
            console.warn('AuthProvider: Stale session detected, forcing sign out');
            await signOut();
            return;
          }
          
          // Log successful authentication for audit
          console.log('AuthProvider: User authenticated:', {
            userId: initialSession.user.id,
            email: initialSession.user.email,
            timestamp: now.toISOString()
          });
          
          // Fetch user profile
          try {
            const rawProfile = await authService.getProfile(initialSession.user.id);
            setProfileSafely(rawProfile);
          } catch (profileError) {
            console.error('AuthProvider: Error fetching profile:', profileError);
            setProfile(null);
          }
        } else {
          console.log('AuthProvider: No active session found');
        }
        
        setSession(initialSession);
        setUser(initialSession?.user ?? null);
      } catch (error) {
        console.error('AuthProvider: Error in getInitialSession:', error);
      } finally {
        setLoading(false);
      }
    };

    getInitialSession();

    // Set up auth state change listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        try {
          console.log(`AuthProvider: Auth state changed - ${event}`);
          
          // Update session and user state first
          setSession(session);
          setUser(session?.user ?? null);
          
          // Handle different auth state changes
          switch (event) {
            case 'SIGNED_IN':
              console.log('AuthProvider: User signed in:', session?.user?.email);
              if (session?.user) {
                try {
                  const rawProfile = await authService.getProfile(session.user.id);
                  setProfileSafely(rawProfile);
                } catch (error) {
                  console.error('AuthProvider: Error fetching profile after sign in:', error);
                  setProfile(null);
                }
              }
              break;
              
            case 'SIGNED_OUT':
              console.log('AuthProvider: User signed out');
              setProfile(null);
              break;
              
            case 'TOKEN_REFRESHED':
              console.log('AuthProvider: Token refreshed');
              break;
              
            case 'USER_UPDATED':
              console.log('AuthProvider: User updated');
              if (session?.user) {
                try {
                  const rawProfile = await authService.getProfile(session.user.id);
                  setProfile(createProfile(rawProfile));
                } catch (error) {
                  console.error('AuthProvider: Error fetching updated profile:', error);
                }
              }
              break;
              
            default:
              console.log('AuthProvider: Unhandled auth state change:', event);
          }
        } catch (error) {
          console.error('AuthProvider: Error in auth state change handler:', error);
        } finally {
          setLoading(false);
        }
      }
    );

    // Cleanup function
    return () => {
      console.log('AuthProvider: Cleaning up auth state listener');
      subscription?.unsubscribe();
    };
  }, [signOut, setProfile]);

  const signIn = useCallback(async (email: string, password: string): Promise<SignInResponse> => {
    console.log('AuthProvider: Attempting to sign in with email:', email);
    
    // Input validation
    if (!email || !password) {
      const error = new Error('Email and password are required');
      console.error(error.message);
      return { data: null, error };
    }
    
    if (!isSupabaseConfigured()) {
      const error = new Error('Authentication service is not properly configured');
      console.error(error.message);
      return { data: null, error };
    }
    
    if (!supabase) {
      const error = new Error('Authentication service is not available');
      console.error(error.message);
      return { data: null, error };
    }
    
    try {
      const { data, error: signInError } = await authService.signIn(email, password);
      
      if (signInError) {
        console.error('AuthProvider: Sign in failed:', signInError.message);
        return { data: null, error: signInError };
      }
      
      if (!data?.user) {
        const error = new Error('No user data returned from authentication');
        console.error(error.message);
        return { data: null, error };
      }
      
      console.log('AuthProvider: Sign in successful for:', email);
      return { data: { user: data.user }, error: null };
      
    } catch (error) {
      const errorMessage = getErrorMessage(error);
      console.error('AuthProvider: Unexpected error during sign in:', errorMessage);
      return { 
        data: null, 
        error: new Error(`Sign in failed: ${errorMessage}`)
      };
    }
  }, []);

  const signUp = useCallback(async (
    email: string, 
    password: string, 
    userData: { full_name?: string; role?: Profile['role'] } = {}
  ): Promise<SignUpResponse> => {
    console.log('AuthProvider: Attempting to sign up with email:', email);
    
    // Input validation
    if (!email || !password) {
      const error = new Error('Email and password are required');
      console.error(error.message);
      return { data: null, error };
    }
    
    if (!isSupabaseConfigured()) {
      const error = new Error('Authentication service is not properly configured');
      console.error(error.message);
      return { data: null, error };
    }
    
    if (!supabase) {
      const error = new Error('Authentication service is not available');
      console.error(error.message);
      return { data: null, error };
    }
    
    // Normalize role
    const normalizedUserData = {
      ...userData,
      role: userData.role || 'student' // Default to 'student' if not provided
    };
    
    try {
      const { data, error: signUpError } = await authService.signUp(email, password, normalizedUserData);
      
      if (signUpError) {
        console.error('AuthProvider: Sign up failed:', signUpError.message);
        return { data: null, error: signUpError };
      }
      
      if (!data?.user) {
        const error = new Error('No user data returned from registration');
        console.error(error.message);
        return { data: null, error };
      }
      
      console.log('AuthProvider: Sign up successful for:', email);
      
      // Log successful registration (without sensitive data)
      console.log('New user registered:', {
        userId: data.user.id,
        email: email.substring(0, 3) + '***',
        role: normalizedUserData.role,
        timestamp: new Date().toISOString()
      });
      
      return { data: { user: data.user }, error: null };
      
    } catch (error) {
      const errorMessage = getErrorMessage(error);
      console.error('AuthProvider: Unexpected error during sign up:', errorMessage);
      return { 
        data: null, 
        error: new Error(`Sign up failed: ${errorMessage}`)
      };
    }
  }, []);


  // Memoize the context value to prevent unnecessary re-renders
  const value = useMemo(() => ({
    user,
    profile,
    session,
    loading,
    signIn,
    signUp,
    signOut,
  }), [user, profile, session, loading, signIn, signUp, signOut]);

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
