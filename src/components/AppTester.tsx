import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, RefreshCw, Play } from 'lucide-react';
import { generateTestReport, TestResult } from '@/utils/testApp';

const AppTester = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);
  const [summary, setSummary] = useState<{ total: number; passed: number; failed: number } | null>(null);

  const runTests = async () => {
    setIsRunning(true);
    setResults([]);
    setSummary(null);

    try {
      const report = await generateTestReport();
      setResults(report.results);
      setSummary(report.summary);
    } catch (error) {
      console.error('Failed to run tests:', error);
      setResults([{
        test: 'Test Runner',
        success: false,
        message: 'Failed to run tests',
        error: error instanceof Error ? error.message : 'Unknown error'
      }]);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="h-4 w-4 text-green-400" />
    ) : (
      <XCircle className="h-4 w-4 text-red-400" />
    );
  };

  const getStatusColor = (success: boolean) => {
    return success ? 'text-green-400' : 'text-red-400';
  };

  return (
    <div className="p-4 space-y-4">
      <Card className="bg-gray-800 border-gray-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Play className="h-5 w-5" />
            Application Test Suite
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-4">
            <Button
              onClick={runTests}
              disabled={isRunning}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isRunning ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Running Tests...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Run All Tests
                </>
              )}
            </Button>

            {summary && (
              <div className="flex items-center gap-4 text-sm">
                <Badge variant="outline" className="text-white border-gray-600">
                  Total: {summary.total}
                </Badge>
                <Badge variant="outline" className="text-green-400 border-green-400">
                  Passed: {summary.passed}
                </Badge>
                <Badge variant="outline" className="text-red-400 border-red-400">
                  Failed: {summary.failed}
                </Badge>
              </div>
            )}
          </div>

          {results.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-white">Test Results</h3>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {results.map((result, index) => (
                  <div
                    key={index}
                    className="flex items-start gap-3 p-3 bg-gray-900/50 rounded-lg border border-gray-700"
                  >
                    <div className="flex-shrink-0 mt-0.5">
                      {getStatusIcon(result.success)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-white">{result.test}</span>
                        <Badge
                          variant="outline"
                          className={`text-xs ${
                            result.success
                              ? 'text-green-400 border-green-400'
                              : 'text-red-400 border-red-400'
                          }`}
                        >
                          {result.success ? 'PASS' : 'FAIL'}
                        </Badge>
                      </div>
                      <p className={`text-sm ${getStatusColor(result.success)}`}>
                        {result.message}
                      </p>
                      {result.error && (
                        <p className="text-xs text-gray-400 mt-1 font-mono">
                          Error: {result.error}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {summary && (
            <div className="mt-4 p-4 bg-gray-900/50 rounded-lg border border-gray-700">
              <h4 className="font-semibold text-white mb-2">Summary</h4>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-white">{summary.total}</div>
                  <div className="text-sm text-gray-400">Total Tests</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-400">{summary.passed}</div>
                  <div className="text-sm text-gray-400">Passed</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-red-400">{summary.failed}</div>
                  <div className="text-sm text-gray-400">Failed</div>
                </div>
              </div>
              
              {summary.failed === 0 ? (
                <div className="mt-4 p-3 bg-green-900/20 border border-green-500/20 rounded-lg">
                  <p className="text-green-400 text-center font-medium">
                    🎉 All tests passed! Your application is working correctly.
                  </p>
                </div>
              ) : (
                <div className="mt-4 p-3 bg-red-900/20 border border-red-500/20 rounded-lg">
                  <p className="text-red-400 text-center font-medium">
                    ⚠️ Some tests failed. Check the details above for issues to fix.
                  </p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AppTester;
