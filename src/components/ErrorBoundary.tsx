
import React, { Component, ReactNode } from 'react';
import { Button } from '@/components/ui/button';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    console.error('Error caught by boundary:', error);
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      const errorMessage = this.state.error?.message || 'An unexpected error occurred';

      return this.props.fallback || (
        <div className="min-h-screen bg-[#121212] flex items-center justify-center p-4">
          <div className="text-center max-w-md">
            <div className="text-6xl mb-4">⚠️</div>
            <h2 className="text-xl font-bold text-white mb-4">Error Loading Content</h2>
            <div className="space-y-4 text-[#E0E0E0]">
              <p className="mb-4">
                Something went wrong. Please try refreshing the page.
              </p>
              <p className="text-sm text-red-400 mb-4">
                Error: {errorMessage}
              </p>
              <div className="space-y-2">
                <p className="text-sm">Try these steps:</p>
                <div className="text-left space-y-1">
                  <div className="flex items-center gap-2">
                    <span className="text-[#00E676]">1.</span>
                    <span>Refresh the page</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-[#00E676]">2.</span>
                    <span>Check your internet connection</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-[#00E676]">3.</span>
                    <span>Clear browser cache</span>
                  </div>
                </div>
              </div>
              <Button
                onClick={() => window.location.reload()}
                className="bg-[#00E676] text-black hover:bg-[#00E676]/90 mt-4"
              >
                Refresh Page
              </Button>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
