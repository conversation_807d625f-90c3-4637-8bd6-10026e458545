import React, { useEffect, useState } from 'react';
import { supabase, isSupabaseConfigured } from '@/integrations/supabase/client';
import { setupDatabase, checkDatabaseHealth } from '@/utils/setupDatabase';
import { Button } from '@/components/ui/button';

const TestSupabase = () => {
  const [status, setStatus] = useState('Loading...');
  const [error, setError] = useState<string | null>(null);
  const [healthStatus, setHealthStatus] = useState<any>(null);
  const [isSettingUp, setIsSettingUp] = useState(false);

  useEffect(() => {
    const testConnection = async () => {
      try {
        console.log('Testing Supabase connection...');
        
        if (!isSupabaseConfigured()) {
          setStatus('Supabase is not properly configured');
          setError('Missing Supabase configuration. Check your environment variables.');
          return;
        }

        if (!supabase) {
          setStatus('Supabase client is not initialized');
          setError('Failed to initialize Supabase client');
          return;
        }

        setStatus('Connecting to Supabase...');
        console.log('Supabase client:', supabase);
        
        // Test auth first
        setStatus('Testing authentication...');
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) {
          console.warn('Session error (may be expected if not logged in):', sessionError);
        }
        
        // Test database health
        setStatus('Testing database health...');
        const health = await checkDatabaseHealth();
        setHealthStatus(health);

        if (!health.healthy) {
          setError(`Database health check failed: ${health.error}`);
          setStatus('Database issues detected');
        } else {
          setStatus('Connected to Supabase successfully!');
          console.log('Database health check passed');
        }
      } catch (err) {
        console.error('Connection test error:', err);
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        setError(`Unexpected error: ${errorMessage}`);
        setStatus('Connection failed');
      }
    };

    testConnection();
  }, []);

  const handleSetupDatabase = async () => {
    setIsSettingUp(true);
    try {
      const result = await setupDatabase();
      if (result.success) {
        setStatus('Database setup completed successfully!');
        setError(null);
        // Re-run health check
        const health = await checkDatabaseHealth();
        setHealthStatus(health);
      } else {
        setError(`Setup failed: ${result.error}`);
      }
    } catch (err) {
      setError(`Setup error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setIsSettingUp(false);
    }
  };

  return (
    <div className="p-4 bg-gray-800 text-white rounded-lg space-y-4">
      <h2 className="text-xl font-bold mb-2">Supabase Connection Test</h2>
      <p className="mb-2">Status: <span className={status.includes('successfully') ? 'text-green-400' : status.includes('failed') || status.includes('issues') ? 'text-red-400' : 'text-yellow-400'}>{status}</span></p>

      {error && (
        <div className="p-3 bg-red-900/20 border border-red-500/20 rounded">
          <p className="text-red-400 text-sm">{error}</p>
        </div>
      )}

      {healthStatus && (
        <div className="space-y-2">
          <h3 className="font-semibold">Database Tables:</h3>
          {healthStatus.tables?.map((table: any) => (
            <div key={table.table} className="flex items-center justify-between text-sm">
              <span>{table.table}</span>
              <span className={table.success ? 'text-green-400' : 'text-red-400'}>
                {table.success ? '✓' : '✗'}
              </span>
            </div>
          ))}
        </div>
      )}

      {(!healthStatus?.healthy || error) && (
        <Button
          onClick={handleSetupDatabase}
          disabled={isSettingUp}
          className="w-full bg-blue-600 hover:bg-blue-700"
        >
          {isSettingUp ? 'Setting up...' : 'Setup Database'}
        </Button>
      )}
    </div>
  );
};

export default TestSupabase;
