import React, { useEffect, useState } from 'react';
import { supabase, isSupabaseConfigured } from '@/integrations/supabase/client';

const TestSupabase = () => {
  const [status, setStatus] = useState('Loading...');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const testConnection = async () => {
      try {
        console.log('Testing Supabase connection...');
        
        if (!isSupabaseConfigured()) {
          setStatus('Supabase is not properly configured');
          setError('Missing Supabase configuration. Check your environment variables.');
          return;
        }

        if (!supabase) {
          setStatus('Supabase client is not initialized');
          setError('Failed to initialize Supabase client');
          return;
        }

        setStatus('Connecting to Supabase...');
        console.log('Supabase client:', supabase);
        
        // Test auth first
        setStatus('Testing authentication...');
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) {
          console.warn('Session error (may be expected if not logged in):', sessionError);
        }
        
        // Test a simple query
        setStatus('Testing database query...');
        const { data, error } = await supabase.from('subjects').select('*').limit(1);

        if (error) {
          setError(`Database error: ${error.message}`);
          setStatus('Connection failed');
        } else {
          setStatus('Connected to Supabase successfully!');
          console.log('Test data:', data);
        }
      } catch (err) {
        console.error('Connection test error:', err);
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        setError(`Unexpected error: ${errorMessage}`);
        setStatus('Connection failed');
      }
    };

    testConnection();
  }, []);

  return (
    <div className="p-4 bg-gray-800 text-white rounded-lg">
      <h2 className="text-xl font-bold mb-2">Supabase Connection Test</h2>
      <p className="mb-2">Status: {status}</p>
      {error && <p className="text-red-400">{error}</p>}
    </div>
  );
};

export default TestSupabase;
