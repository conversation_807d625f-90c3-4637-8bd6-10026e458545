import { createClient, SupabaseClient } from '@supabase/supabase-js';
import type { Database } from './database.types';

type SupabaseClientType = SupabaseClient<Database>;

/**
 * Supabase client instance
 */
let supabase: SupabaseClientType | null = null;

/**
 * Initialize the Supabase client
 * @throws {Error} If required environment variables are missing
 */
const initializeSupabase = (): void => {
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseAnonKey) {
    const errorMsg = 'Missing Supabase configuration. Please check your environment variables.';
    console.error(errorMsg);
    throw new Error(errorMsg);
  }

  try {
    supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: true,
      },
    });
    console.log('Supabase client initialized successfully');
  } catch (error) {
    const errorMsg = 'Failed to initialize Supabase client';
    console.error(errorMsg, error);
    throw new Error(errorMsg);
  }
};

// Initialize on import
if (import.meta.env.VITE_SUPABASE_URL && import.meta.env.VITE_SUPABASE_ANON_KEY) {
  initializeSupabase();
}

/**
 * Get the Supabase client instance
 * @returns The Supabase client instance
 * @throws {Error} If Supabase is not configured
 */
const getSupabaseClient = (): SupabaseClientType => {
  if (!supabase) {
    throw new Error('Supabase client is not initialized');
  }
  return supabase;
};

/**
 * Check if Supabase is properly configured
 * @returns boolean indicating if Supabase is configured
 */
const isSupabaseConfigured = (): boolean => {
  return Boolean(import.meta.env.VITE_SUPABASE_URL && import.meta.env.VITE_SUPABASE_ANON_KEY);
};

/**
 * Get a public URL for a file in storage
 * @param bucket - The storage bucket name
 * @param filePath - Path to the file in the bucket
 * @returns Public URL for the file or empty string on error
 */
const getFileUrl = (bucket: string, filePath: string): string => {
  if (!supabase) return '';
  const { data, error } = supabase.storage.from(bucket).getPublicUrl(filePath);
  if (error) {
    console.error('Error getting public URL:', error.message);
    return '';
  }
  return data.publicUrl;
};

// Export the supabase client as both default and named export
export { supabase, getSupabaseClient, isSupabaseConfigured, getFileUrl };
