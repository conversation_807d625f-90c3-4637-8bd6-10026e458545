export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          created_at: string | null
          email: string | null
          full_name: string | null
          id: string
          role: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          email?: string | null
          full_name?: string | null
          id: string
          role?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string | null
          full_name?: string | null
          id?: string
          role?: string | null
          updated_at?: string | null
        }
      }
      subjects: {
        Row: {
          color: string | null
          created_at: string
          created_by: string | null
          description: string | null
          grade: number
          icon: string | null
          id: string
          name: string
          updated_at: string
        }
        Insert: {
          color?: string | null
          created_at?: string
          created_by?: string | null
          description?: string | null
          grade: number
          icon?: string | null
          id?: string
          name: string
          updated_at?: string
        }
        Update: {
          color?: string | null
          created_at?: string
          created_by?: string | null
          description?: string | null
          grade?: number
          icon?: string | null
          id?: string
          name?: string
          updated_at?: string
        }
      }
      chapters: {
        Row: {
          created_at: string
          description: string | null
          id: string
          name: string
          order_index: number
          subject_id: string | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          name: string
          order_index: number
          subject_id?: string | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          name?: string
          order_index?: number
          subject_id?: string | null
          updated_at?: string
        }
      }
      study_materials: {
        Row: {
          chapter_id: string | null
          created_at: string
          description: string | null
          file_path: string | null
          grade: number | null
          id: string
          is_public: boolean | null
          subject_id: string | null
          teacher_id: string
          title: string
          type: string
          updated_at: string
          url: string | null
        }
        Insert: {
          chapter_id?: string | null
          created_at?: string
          description?: string | null
          file_path?: string | null
          grade?: number | null
          id?: string
          is_public?: boolean | null
          subject_id?: string | null
          teacher_id: string
          title: string
          type: string
          updated_at?: string
          url?: string | null
        }
        Update: {
          chapter_id?: string | null
          created_at?: string
          description?: string | null
          file_path?: string | null
          grade?: number | null
          id?: string
          is_public?: boolean | null
          subject_id?: string | null
          teacher_id?: string
          title?: string
          type?: string
          updated_at?: string
          url?: string | null
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_current_user_role: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      is_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_teacher: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
  }
}
