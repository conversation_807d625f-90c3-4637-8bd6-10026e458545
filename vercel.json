{"buildCommand": "npm run build", "outputDirectory": "dist", "framework": "vite", "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2)$", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400"}, {"key": "X-Content-Type-Options", "value": "nosniff"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}]}