
# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local development
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs (except dist)
.vite
.cache

# IDE files
.vscode
.idea
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
logs
*.log

# Local server files
server/
uploads/
data/

# Scripts
scripts/

# Documentation
*.md
!README.md

# Test files
src/tests/
*.test.ts
*.test.js

# Development config
.eslintrc*
.prettierrc*
vite.config.ts.timestamp*

# Temporary files
.tmp
temp/
